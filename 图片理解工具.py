#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import json
import requests
import os
import tkinter as tk
from tkinter import filedialog, messagebox

def image_to_base64(image_path):
    """将图片转换为base64编码"""
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
            return f"data:image/jpeg;base64,{encoded_string}"
    except Exception as e:
        raise Exception(f"图片编码失败: {str(e)}")

def analyze_image(image_path):
    """分析图片"""
    api_url = "https://free.v36.cm"
    api_key = "sk-toCwQl8ObRPPSSB798751742EaF844658e83Aa9207A47c65"

    prompt = """请详细描述所提供图片的内容。我希望描述的结构如下：

1. 逐层分解图片。例如："背景是灰色的路面。前景是一只手拿着纸盘。"
2. 列出图片中的每个元素，并附上大致的位置参考。
3. 根据之前生成的描述列表，以优雅的散文风格描述图片内容。
4. **您提供的提示必须以**英语**返回，仅输出最终的图片描述文本，不包含任何列表文本。**"""

    print("🔄 正在转换图片为base64格式...")
    base64_image = image_to_base64(image_path)
    print(f"✅ 图片转换完成，base64长度: {len(base64_image)} 字符")

    print("\n📤 正在发送请求到ChatGPT API...")
    print(f"🌐 API地址: {api_url}/v1/chat/completions")
    print(f"🤖 使用模型: gpt-4o-mini")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    payload = {
        "model": "gpt-4o-mini",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": base64_image
                        }
                    }
                ]
            }
        ],
        "max_tokens": 1000,
        "temperature": 0.7
    }

    print("\n" + "=" * 80)
    print("📋 完整的API请求内容:")
    print("=" * 80)
    print(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
    print(f"\n请求体 (不包含base64图片数据):")
    payload_for_print = payload.copy()
    payload_for_print["messages"][0]["content"][1]["image_url"]["url"] = f"[BASE64图片数据，长度: {len(base64_image)} 字符]"
    print(json.dumps(payload_for_print, indent=2, ensure_ascii=False))
    print("=" * 80)

    try:
        print("\n⏳ 发送请求中，请稍候...")
        response = requests.post(
            f"{api_url}/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )

        print(f"\n📥 收到响应，状态码: {response.status_code}")

        print("\n" + "=" * 80)
        print("📋 完整的API响应内容:")
        print("=" * 80)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            result = response.json()
            print(f"\n完整响应JSON:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            print("=" * 80)

            if result.get("choices") and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                print(f"\n✅ 成功提取AI回复内容，长度: {len(content)} 字符")
                return content
            else:
                print("❌ API返回数据格式错误: 没有找到choices或choices为空")
                raise Exception("API返回数据格式错误")
        else:
            print(f"\n❌ API请求失败:")
            print(f"状态码: {response.status_code}")
            print(f"响应文本: {response.text}")
            print("=" * 80)
            raise Exception(f"API请求失败: {response.status_code} {response.text}")

    except requests.exceptions.Timeout:
        print("❌ 请求超时 (60秒)")
        raise Exception("请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 网络连接错误")
        raise Exception("网络连接错误")
    except Exception as e:
        print(f"❌ 请求过程中发生错误: {str(e)}")
        raise Exception(f"请求失败: {str(e)}")

def select_image_file():
    """使用文件对话框选择图片文件"""
    # 创建一个隐藏的根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 设置文件类型过滤器
    filetypes = [
        ("所有图片文件", "*.jpg *.jpeg *.png *.gif *.bmp *.webp"),
        ("JPEG文件", "*.jpg *.jpeg"),
        ("PNG文件", "*.png"),
        ("GIF文件", "*.gif"),
        ("BMP文件", "*.bmp"),
        ("WebP文件", "*.webp"),
        ("所有文件", "*.*")
    ]

    # 打开文件选择对话框
    file_path = filedialog.askopenfilename(
        title="选择要分析的图片文件",
        filetypes=filetypes,
        initialdir=os.getcwd()
    )

    # 销毁根窗口
    root.destroy()

    return file_path

def main():
    """主函数"""
    print("=" * 60)
    print("🖼️  ChatGPT 图片理解工具 (文件选择版)")
    print("=" * 60)

    while True:
        try:
            print("\n请选择操作:")
            print("1. 📁 打开文件选择对话框")
            print("2. ⌨️  手动输入文件路径")
            print("3. ❌ 退出程序")

            choice = input("\n请输入选项 (1/2/3): ").strip()

            if choice == '3':
                print("程序已退出")
                break
            elif choice == '1':
                # 使用文件对话框选择图片
                print("🔍 正在打开文件选择对话框...")
                image_path = select_image_file()

                if not image_path:
                    print("❌ 未选择文件")
                    continue

            elif choice == '2':
                # 手动输入路径
                image_path = input("\n请输入图片文件路径: ").strip()

                if not image_path:
                    print("❌ 路径不能为空")
                    continue

                # 去除可能的引号
                image_path = image_path.strip('"\'')
            else:
                print("❌ 无效选项，请重新选择")
                continue

            # 检查文件是否存在
            if not os.path.exists(image_path):
                print("❌ 文件不存在，请检查路径是否正确")
                continue

            # 检查是否为图片文件
            valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
            if not any(image_path.lower().endswith(ext) for ext in valid_extensions):
                print("❌ 请选择有效的图片文件 (支持: jpg, jpeg, png, gif, bmp, webp)")
                continue

            print(f"\n📁 已选择图片: {os.path.basename(image_path)}")
            print(f"📍 完整路径: {image_path}")

            # 分析图片
            print("\n🔍 开始分析图片...")
            result = analyze_image(image_path)

            print("\n" + "=" * 80)
            print("📝 AI 分析结果:")
            print("=" * 80)
            print(result)
            print("=" * 80)

        except KeyboardInterrupt:
            print("\n\n程序已中断")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
            print("请检查网络连接和API配置")

if __name__ == "__main__":
    main()
