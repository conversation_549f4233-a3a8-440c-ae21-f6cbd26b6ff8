#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import json
import requests
import os

def image_to_base64(image_path):
    """将图片转换为base64编码"""
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
            return f"data:image/jpeg;base64,{encoded_string}"
    except Exception as e:
        raise Exception(f"图片编码失败: {str(e)}")

def analyze_image(image_path):
    """分析图片"""
    api_url = "https://free.v36.cm"
    api_key = "sk-toCwQl8ObRPPSSB798751742EaF844658e83Aa9207A47c65"

    prompt = """请详细描述所提供图片的内容。我希望描述的结构如下：

1. 逐层分解图片。例如："背景是灰色的路面。前景是一只手拿着纸盘。"
2. 列出图片中的每个元素，并附上大致的位置参考。
3. 根据之前生成的描述列表，以优雅的散文风格描述图片内容。
4. **您提供的提示必须以**英语**返回，仅输出最终的图片描述文本，不包含任何列表文本。**"""

    print("正在转换图片为base64格式...")
    base64_image = image_to_base64(image_path)

    print("正在发送请求到ChatGPT API...")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    payload = {
        "model": "gpt-4o-mini",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": base64_image
                        }
                    }
                ]
            }
        ],
        "max_tokens": 1000,
        "temperature": 0.7
    }

    try:
        response = requests.post(
            f"{api_url}/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("choices") and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                raise Exception("API返回数据格式错误")
        else:
            raise Exception(f"API请求失败: {response.status_code} {response.text}")

    except Exception as e:
        raise Exception(f"请求失败: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("🖼️  ChatGPT 图片理解工具 (控制台版)")
    print("=" * 60)

    while True:
        try:
            # 获取图片路径
            image_path = input("\n请输入图片文件路径 (或输入 'q' 退出): ").strip()

            if image_path.lower() == 'q':
                print("程序已退出")
                break

            # 去除可能的引号
            image_path = image_path.strip('"\'')

            # 检查文件是否存在
            if not os.path.exists(image_path):
                print("❌ 文件不存在，请检查路径是否正确")
                continue

            # 检查是否为图片文件
            valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
            if not any(image_path.lower().endswith(ext) for ext in valid_extensions):
                print("❌ 请选择有效的图片文件 (支持: jpg, jpeg, png, gif, bmp, webp)")
                continue

            print(f"\n📁 已选择图片: {os.path.basename(image_path)}")

            # 分析图片
            print("🔍 正在分析图片...")
            result = analyze_image(image_path)

            print("\n" + "=" * 60)
            print("📝 AI 分析结果:")
            print("=" * 60)
            print(result)
            print("=" * 60)

        except KeyboardInterrupt:
            print("\n\n程序已中断")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
